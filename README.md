# 简易论坛系统 - 宝塔面板部署教程

## 📋 项目简介

这是一个基于PHP+MySQL开发的简易论坛系统，具有以下特点：
- 用户注册/登录系统
- 话题发布与回复功能
- 响应式设计，支持移动端
- 基于Bootstrap 5的现代化UI
- 安全的用户认证和数据处理

## 🛠 系统要求

### 服务器环境
- **操作系统**: Linux (推荐 Ubuntu 18.04+ 或 CentOS 7+)
- **Web服务器**: Nginx 或 Apache
- **PHP版本**: PHP 7.4+ (推荐 PHP 8.0+)
- **数据库**: MySQL 5.7+ 或 MariaDB 10.3+
- **内存**: 最低 1GB RAM
- **存储**: 最低 10GB 可用空间

### PHP扩展要求
- `php-mysql` (PDO MySQL支持)
- `php-mbstring` (多字节字符串支持)
- `php-json` (JSON支持)
- `php-session` (会话支持)

## 📦 部署包内容

```
forum_deployment/
├── forum.example.com/          # 网站源码目录
│   ├── config.php             # 数据库配置文件
│   ├── functions.php          # 核心功能函数
│   ├── index.php              # 主入口文件
│   ├── security.php           # 安全防护模块
│   ├── pages/                 # 页面文件目录
│   │   ├── home.php          # 首页
│   │   ├── login.php         # 登录页面
│   │   ├── register.php      # 注册页面
│   │   ├── new_topic.php     # 发布话题
│   │   └── topic.php         # 话题详情
│   └── security/             # 安全日志目录
├── forum_database_structure.sql # 数据库结构文件
└── README.md                  # 本部署文档
```

## 🚀 宝塔面板部署步骤

### 第一步：安装宝塔面板

如果服务器还没有安装宝塔面板，请先安装：

```bash
# CentOS/RHEL
yum install -y wget && wget -O install.sh http://download.bt.cn/install/install_6.0.sh && sh install.sh

# Ubuntu/Debian  
wget -O install.sh http://download.bt.cn/install/install-ubuntu_6.0.sh && sudo bash install.sh
```

### 第二步：配置运行环境

1. **登录宝塔面板**
   - 安装完成后，访问面板地址
   - 使用安装时显示的用户名和密码登录

2. **安装LNMP环境**
   - 进入"软件商店"
   - 安装以下组件：
     - **Nginx** (推荐最新稳定版)
     - **MySQL** (5.7或8.0版本)
     - **PHP** (8.0或8.1版本)
     - **phpMyAdmin** (数据库管理工具)

3. **配置PHP**
   - 进入"软件商店" → "PHP设置"
   - 确保以下扩展已安装：
     - `mysqli`
     - `pdo_mysql` 
     - `mbstring`
     - `json`
     - `session`

### 第三步：创建网站

1. **添加站点**
   - 进入"网站" → "添加站点"
   - 域名：填写你的域名（如：forum.yourdomain.com）
   - 根目录：选择或创建网站目录
   - PHP版本：选择已安装的PHP版本
   - 数据库：选择"创建数据库"

2. **记录数据库信息**
   - 数据库名：`forum_db`（或自定义）
   - 用户名：自动生成
   - 密码：自动生成
   - **请务必记录这些信息！**

### 第四步：上传源码

1. **上传文件**
   - 进入"文件" → 找到网站根目录
   - 上传 `forum_source_*.tar.gz` 压缩包
   - 解压缩包：右键 → "解压"

2. **移动文件**
   ```bash
   # 将forum.example.com目录下的所有文件移动到网站根目录
   mv forum.example.com/* ./
   rm -rf forum.example.com/
   ```

### 第五步：配置数据库

1. **导入数据库结构**
   - 进入"数据库" → 点击数据库名 → "管理"
   - 在phpMyAdmin中导入 `forum_database_structure.sql`

2. **修改配置文件**
   - 编辑网站根目录下的 `config.php`
   - 修改数据库连接信息：

```php
<?php
// 数据库配置 - 请修改为你的实际信息
define('DB_HOST', 'localhost');
define('DB_NAME', '你的数据库名');        // 第三步记录的数据库名
define('DB_USER', '你的数据库用户名');     // 第三步记录的用户名  
define('DB_PASS', '你的数据库密码');       // 第三步记录的密码

// 站点配置
define('SITE_NAME', '简易论坛');
define('SITE_URL', 'https://你的域名');    // 修改为你的实际域名

// 连接数据库
try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("数据库连接失败: " . $e->getMessage());
}
?>
```

### 第六步：设置目录权限

1. **设置权限**
   - 进入"文件" → 选择网站根目录
   - 右键 → "权限" → 设置为 `755`
   - 勾选"应用到子目录"

2. **设置安全目录权限**
   ```bash
   chmod 777 security/
   ```

### 第七步：配置SSL证书（推荐）

1. **申请SSL证书**
   - 进入"网站" → 找到你的站点 → "设置"
   - 点击"SSL" → "Let's Encrypt"
   - 填写邮箱并申请免费证书

2. **强制HTTPS**
   - 证书申请成功后，开启"强制HTTPS"

### 第八步：测试访问

1. **访问网站**
   - 打开浏览器，访问你的域名
   - 应该能看到论坛首页

2. **测试功能**
   - 注册新用户
   - 登录系统
   - 发布话题
   - 回复测试

## 🔧 常见问题解决

### 数据库连接失败
- 检查 `config.php` 中的数据库信息是否正确
- 确认数据库服务是否正常运行
- 检查数据库用户权限

### 页面显示空白
- 检查PHP错误日志：`/www/wwwlogs/你的域名.error.log`
- 确认PHP版本兼容性
- 检查文件权限设置

### 无法上传文件
- 检查 `security/` 目录权限是否为 777
- 确认磁盘空间是否充足

### 样式显示异常
- 检查CDN资源是否能正常加载
- 确认网络连接正常

## 🛡 安全建议

1. **定期更新**
   - 保持PHP、MySQL、Nginx版本最新
   - 定期更新宝塔面板

2. **备份数据**
   - 设置定期数据库备份
   - 备份网站文件

3. **监控日志**
   - 定期检查访问日志和错误日志
   - 关注异常访问

4. **防火墙设置**
   - 只开放必要端口（80, 443, 22等）
   - 设置IP白名单（如有需要）

## 📞 技术支持

如果在部署过程中遇到问题，请检查：
1. 服务器系统日志
2. 宝塔面板错误日志  
3. PHP错误日志
4. 数据库连接状态

---

**部署完成后，你将拥有一个功能完整的论坛系统！** 🎉
