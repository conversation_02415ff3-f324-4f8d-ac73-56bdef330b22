<?php
// 数据库安装脚本
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'forum_db';

try {
    // 连接MySQL服务器
    $pdo = new PDO("mysql:host=$host", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // 创建数据库
    $pdo->exec("CREATE DATABASE IF NOT EXISTS $database CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "数据库创建成功！<br>";
    
    // 选择数据库
    $pdo->exec("USE $database");
    
    // 创建用户表
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "用户表创建成功！<br>";
    
    // 创建话题表
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS topics (
            id INT AUTO_INCREMENT PRIMARY KEY,
            title VARCHAR(255) NOT NULL,
            user_id INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "话题表创建成功！<br>";
    
    // 创建帖子表
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS posts (
            id INT AUTO_INCREMENT PRIMARY KEY,
            topic_id INT NOT NULL,
            user_id INT NOT NULL,
            content TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (topic_id) REFERENCES topics(id) ON DELETE CASCADE,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "帖子表创建成功！<br>";
    
    // 插入示例数据
    // 创建管理员用户
    $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
    $stmt = $pdo->prepare("INSERT IGNORE INTO users (username, email, password) VALUES (?, ?, ?)");
    $stmt->execute(['admin', '<EMAIL>', $admin_password]);
    echo "管理员用户创建成功！用户名: admin, 密码: admin123<br>";
    
    // 创建示例话题
    $stmt = $pdo->prepare("INSERT IGNORE INTO topics (title, user_id) VALUES (?, ?)");
    $stmt->execute(['欢迎来到简易论坛！', 1]);
    $topic_id = $pdo->lastInsertId();
    
    if ($topic_id) {
        // 创建示例帖子
        $stmt = $pdo->prepare("INSERT INTO posts (topic_id, user_id, content) VALUES (?, ?, ?)");
        $stmt->execute([$topic_id, 1, '欢迎大家来到我们的论坛！这是一个现代化的论坛系统，支持移动端和PC端访问。

在这里您可以：
- 发布话题和参与讨论
- 与其他用户交流互动
- 分享知识和经验

让我们一起创建一个友好的社区环境！']);
        echo "示例话题和帖子创建成功！<br>";
    }
    
    echo "<br><strong>安装完成！</strong><br>";
    echo "请访问 <a href='index.php'>论坛首页</a><br>";
    echo "管理员登录信息：<br>";
    echo "用户名: admin<br>";
    echo "密码: admin123<br>";
    
} catch(PDOException $e) {
    echo "安装失败: " . $e->getMessage();
}
?>
