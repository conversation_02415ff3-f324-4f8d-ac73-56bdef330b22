<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="bi bi-chat-dots"></i> 话题管理</h2>
    <div>
        <span class="badge bg-success">总话题: <?php echo $stats['total_topics']; ?></span>
        <span class="badge bg-warning">今日新增: <?php echo $stats['today_topics']; ?></span>
    </div>
</div>

<?php
$topics = $pdo->query("
    SELECT t.*, u.username,
           COUNT(p.id) as reply_count,
           MAX(p.created_at) as last_reply
    FROM topics t
    JOIN users u ON t.user_id = u.id
    LEFT JOIN posts p ON t.id = p.topic_id
    GROUP BY t.id
    ORDER BY t.created_at DESC
")->fetchAll();
?>

<div class="card">
    <div class="card-header">
        <h5><i class="bi bi-table"></i> 话题列表</h5>
    </div>
    <div class="card-body">
        <?php if ($topics): ?>
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>ID</th>
                            <th>标题</th>
                            <th>作者</th>
                            <th>创建时间</th>
                            <th>回复数</th>
                            <th>最后回复</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($topics as $topic): ?>
                            <tr>
                                <td><?php echo $topic['id']; ?></td>
                                <td>
                                    <a href="../index.php?page=topic&id=<?php echo $topic['id']; ?>" 
                                       target="_blank" class="text-decoration-none">
                                        <?php echo safe_output($topic['title']); ?>
                                    </a>
                                </td>
                                <td>
                                    <strong><?php echo safe_output($topic['username']); ?></strong>
                                </td>
                                <td>
                                    <small><?php echo date('Y-m-d H:i', strtotime($topic['created_at'])); ?></small>
                                </td>
                                <td>
                                    <span class="badge bg-info"><?php echo $topic['reply_count']; ?></span>
                                </td>
                                <td>
                                    <?php if ($topic['last_reply']): ?>
                                        <small><?php echo time_ago($topic['last_reply']); ?></small>
                                    <?php else: ?>
                                        <small class="text-muted">无回复</small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="../index.php?page=topic&id=<?php echo $topic['id']; ?>" 
                                           target="_blank" class="btn btn-sm btn-outline-primary">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                        <form method="POST" action="?action=topics" style="display: inline;" 
                                              onsubmit="return confirmDelete('确定要删除话题「<?php echo safe_output($topic['title']); ?>」吗？')">
                                            <input type="hidden" name="topic_id" value="<?php echo $topic['id']; ?>">
                                            <button type="submit" name="action" value="delete_topic" 
                                                    class="btn btn-sm btn-danger">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php else: ?>
            <div class="text-center py-4">
                <i class="bi bi-chat-dots text-muted" style="font-size: 3rem;"></i>
                <p class="text-muted mt-2">暂无话题数据</p>
            </div>
        <?php endif; ?>
    </div>
</div>
