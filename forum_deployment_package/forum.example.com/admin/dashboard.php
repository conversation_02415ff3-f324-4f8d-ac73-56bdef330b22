<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="bi bi-speedometer2"></i> 仪表盘</h2>
    <span class="text-muted">欢迎，<?php echo safe_output($_SESSION['username']); ?>！</span>
</div>

<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <i class="bi bi-people" style="font-size: 2rem;"></i>
                <h3 class="mt-2"><?php echo $stats['total_users']; ?></h3>
                <p class="mb-0">总用户数</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <i class="bi bi-chat-dots" style="font-size: 2rem;"></i>
                <h3 class="mt-2"><?php echo $stats['total_topics']; ?></h3>
                <p class="mb-0">总话题数</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <i class="bi bi-chat-left-text" style="font-size: 2rem;"></i>
                <h3 class="mt-2"><?php echo $stats['total_posts']; ?></h3>
                <p class="mb-0">总帖子数</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <i class="bi bi-calendar-day" style="font-size: 2rem;"></i>
                <h3 class="mt-2"><?php echo $stats['today_topics']; ?></h3>
                <p class="mb-0">今日新话题</p>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-clock-history"></i> 最新话题</h5>
            </div>
            <div class="card-body">
                <?php
                $latest_topics = $pdo->query("
                    SELECT t.*, u.username 
                    FROM topics t 
                    JOIN users u ON t.user_id = u.id 
                    ORDER BY t.created_at DESC 
                    LIMIT 5
                ")->fetchAll();
                
                if ($latest_topics): ?>
                    <div class="list-group list-group-flush">
                        <?php foreach ($latest_topics as $topic): ?>
                            <div class="list-group-item d-flex justify-content-between align-items-start">
                                <div>
                                    <h6 class="mb-1"><?php echo safe_output($topic['title']); ?></h6>
                                    <small class="text-muted">
                                        by <?php echo safe_output($topic['username']); ?> - 
                                        <?php echo time_ago($topic['created_at']); ?>
                                    </small>
                                </div>
                                <a href="../index.php?page=topic&id=<?php echo $topic['id']; ?>" 
                                   class="btn btn-sm btn-outline-primary" target="_blank">查看</a>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <p class="text-muted">暂无话题</p>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-person-plus"></i> 最新用户</h5>
            </div>
            <div class="card-body">
                <?php
                $latest_users = $pdo->query("
                    SELECT * FROM users 
                    ORDER BY created_at DESC 
                    LIMIT 5
                ")->fetchAll();
                
                if ($latest_users): ?>
                    <div class="list-group list-group-flush">
                        <?php foreach ($latest_users as $user): ?>
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1"><?php echo safe_output($user['username']); ?></h6>
                                    <small class="text-muted"><?php echo safe_output($user['email']); ?></small>
                                </div>
                                <small class="text-muted"><?php echo time_ago($user['created_at']); ?></small>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <p class="text-muted">暂无用户</p>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-info-circle"></i> 系统信息</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>PHP版本：</strong> <?php echo phpversion(); ?></p>
                        <p><strong>服务器时间：</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>数据库：</strong> MySQL</p>
                        <p><strong>论坛版本：</strong> 1.0.0</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
