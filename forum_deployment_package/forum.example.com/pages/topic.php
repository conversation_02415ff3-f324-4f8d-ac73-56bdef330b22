<?php
$topic_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
if (!$topic_id) {
    echo '<div class="alert alert-danger">话题不存在</div>';
    return;
}

$topic = get_topic($topic_id);
if (!$topic) {
    echo '<div class="alert alert-danger">话题不存在</div>';
    return;
}

$posts = get_topic_posts($topic_id);

$error = '';
$success = '';

// 处理回复
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['reply'])) {
    if (!is_logged_in()) {
        $error = '请先登录后再回复';
    } else {
        $content = trim($_POST['content']);
        if (empty($content)) {
            $error = '回复内容不能为空';
        } elseif (strlen($content) < 5) {
            $error = '回复内容至少需要5个字符';
        } else {
            if (add_reply($topic_id, $content, $_SESSION['user_id'])) {
                $success = '回复成功！';
                // 重新获取帖子列表
                $posts = get_topic_posts($topic_id);
            } else {
                $error = '回复失败，请重试';
            }
        }
    }
}
?>

<div class="row">
    <div class="col-lg-8">
        <!-- 话题标题 -->
        <div class="card mb-4">
            <div class="card-header">
                <h3 class="mb-0"><?php echo safe_output($topic['title']); ?></h3>
                <small class="text-muted">
                    <i class="bi bi-person"></i> <?php echo safe_output($topic['username']); ?>
                    <i class="bi bi-clock ms-2"></i> <?php echo time_ago($topic['created_at']); ?>
                </small>
            </div>
        </div>

        <!-- 帖子列表 -->
        <?php foreach ($posts as $index => $post): ?>
        <div class="card mb-3">
            <div class="card-body">
                <div class="d-flex align-items-start">
                    <div class="user-avatar bg-primary text-white d-flex align-items-center justify-content-center me-3">
                        <?php echo strtoupper(substr($post['username'], 0, 1)); ?>
                    </div>
                    <div class="flex-grow-1">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h6 class="mb-0"><?php echo safe_output($post['username']); ?></h6>
                            <small class="text-muted">
                                <?php if ($index == 0): ?>
                                    <span class="badge bg-success">楼主</span>
                                <?php else: ?>
                                    #<?php echo $index + 1; ?>
                                <?php endif; ?>
                                <?php echo time_ago($post['created_at']); ?>
                            </small>
                        </div>
                        <div class="post-content">
                            <?php echo nl2br(safe_output($post['content'])); ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php endforeach; ?>

        <!-- 回复表单 -->
        <?php if (is_logged_in()): ?>
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-reply"></i> 发表回复</h5>
            </div>
            <div class="card-body">
                <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle"></i> <?php echo $error; ?>
                </div>
                <?php endif; ?>
                
                <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="bi bi-check-circle"></i> <?php echo $success; ?>
                </div>
                <?php endif; ?>

                <form method="POST">
                    <div class="mb-3">
                        <textarea class="form-control" name="content" rows="4" 
                                  placeholder="请输入回复内容..." required minlength="5"></textarea>
                        <div class="form-text">至少5个字符</div>
                    </div>
                    <div class="d-flex justify-content-between">
                        <a href="index.php?page=topics" class="btn btn-secondary">
                            <i class="bi bi-arrow-left"></i> 返回话题列表
                        </a>
                        <button type="submit" name="reply" class="btn btn-primary">
                            <i class="bi bi-send"></i> 发表回复
                        </button>
                    </div>
                </form>
            </div>
        </div>
        <?php else: ?>
        <div class="card">
            <div class="card-body text-center">
                <p class="mb-3">请登录后参与讨论</p>
                <a href="index.php?page=login" class="btn btn-primary">
                    <i class="bi bi-box-arrow-in-right"></i> 登录
                </a>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <!-- 侧边栏 -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6><i class="bi bi-info-circle"></i> 话题信息</h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <strong>发布者：</strong> <?php echo safe_output($topic['username']); ?>
                    </li>
                    <li class="mb-2">
                        <strong>发布时间：</strong> <?php echo date('Y-m-d H:i', strtotime($topic['created_at'])); ?>
                    </li>
                    <li class="mb-0">
                        <strong>回复数：</strong> <?php echo count($posts) - 1; ?>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
