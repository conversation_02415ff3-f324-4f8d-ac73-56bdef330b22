<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="bi bi-chat-dots"></i> 所有话题</h2>
    <?php if (is_logged_in()): ?>
    <a href="index.php?page=new_topic" class="btn btn-primary">
        <i class="bi bi-plus-circle"></i> 发布新话题
    </a>
    <?php endif; ?>
</div>

<?php
$topics = get_latest_topics(50);
if (empty($topics)):
?>
<div class="card">
    <div class="card-body text-center py-5">
        <i class="bi bi-chat-dots-fill text-muted" style="font-size: 4rem;"></i>
        <h4 class="mt-3 text-muted">还没有话题</h4>
        <p class="text-muted">成为第一个发布话题的用户吧！</p>
        <?php if (is_logged_in()): ?>
        <a href="index.php?page=new_topic" class="btn btn-primary">
            <i class="bi bi-plus-circle"></i> 发布话题
        </a>
        <?php else: ?>
        <a href="index.php?page=login" class="btn btn-primary">
            <i class="bi bi-box-arrow-in-right"></i> 登录后发布
        </a>
        <?php endif; ?>
    </div>
</div>
<?php else: ?>
<div class="card">
    <div class="card-body p-0">
        <div class="list-group list-group-flush">
            <?php foreach ($topics as $topic): ?>
            <div class="list-group-item">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h5 class="mb-1">
                            <a href="index.php?page=topic&id=<?php echo $topic['id']; ?>" class="topic-title">
                                <?php echo safe_output($topic['title']); ?>
                            </a>
                        </h5>
                        <p class="mb-1 post-meta">
                            <i class="bi bi-person"></i> <?php echo safe_output($topic['username']); ?>
                            <i class="bi bi-clock ms-3"></i> <?php echo time_ago($topic['created_at']); ?>
                        </p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <span class="badge bg-primary rounded-pill">
                            <i class="bi bi-chat"></i> <?php echo $topic['reply_count']; ?> 回复
                        </span>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
</div>
<?php endif; ?>
