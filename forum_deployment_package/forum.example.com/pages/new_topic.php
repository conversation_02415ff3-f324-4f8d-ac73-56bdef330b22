<?php
if (!is_logged_in()) {
    echo '<div class="alert alert-warning">请先登录后再发布话题。</div>';
    echo '<script>setTimeout(function(){ window.location.href = "index.php?page=login"; }, 2000);</script>';
    return;
}

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $title = trim($_POST['title']);
    $content = trim($_POST['content']);
    
    if (empty($title) || empty($content)) {
        $error = '请填写标题和内容';
    } elseif (strlen($title) < 5) {
        $error = '标题至少需要5个字符';
    } elseif (strlen($content) < 10) {
        $error = '内容至少需要10个字符';
    } else {
        $topic_id = create_topic($title, $content, $_SESSION['user_id']);
        if ($topic_id) {
            $success = '话题发布成功！正在跳转...';
            echo '<script>setTimeout(function(){ window.location.href = "index.php?page=topic&id=' . $topic_id . '"; }, 1500);</script>';
        } else {
            $error = '发布失败，请重试';
        }
    }
}
?>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h4><i class="bi bi-plus-circle"></i> 发布新话题</h4>
            </div>
            <div class="card-body">
                <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle"></i> <?php echo $error; ?>
                </div>
                <?php endif; ?>
                
                <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="bi bi-check-circle"></i> <?php echo $success; ?>
                </div>
                <?php endif; ?>

                <form method="POST">
                    <div class="mb-3">
                        <label for="title" class="form-label">话题标题</label>
                        <input type="text" class="form-control" id="title" name="title" 
                               value="<?php echo isset($_POST['title']) ? safe_output($_POST['title']) : ''; ?>" 
                               placeholder="请输入话题标题..." required minlength="5">
                        <div class="form-text">至少5个字符</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="content" class="form-label">话题内容</label>
                        <textarea class="form-control" id="content" name="content" rows="8" 
                                  placeholder="请输入话题内容..." required minlength="10"><?php echo isset($_POST['content']) ? safe_output($_POST['content']) : ''; ?></textarea>
                        <div class="form-text">至少10个字符，支持换行</div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="index.php" class="btn btn-secondary">
                            <i class="bi bi-arrow-left"></i> 返回
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-send"></i> 发布话题
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6><i class="bi bi-info-circle"></i> 发布须知</h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success"></i> 标题要简洁明了
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success"></i> 内容要详细具体
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success"></i> 遵守社区规则
                    </li>
                    <li class="mb-0">
                        <i class="bi bi-check-circle text-success"></i> 友善交流讨论
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
