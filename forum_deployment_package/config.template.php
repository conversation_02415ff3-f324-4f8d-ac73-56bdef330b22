<?php
/**
 * 简易论坛系统 - 配置文件模板
 * 
 * 部署说明：
 * 1. 将此文件重命名为 config.php
 * 2. 修改下面的数据库连接信息
 * 3. 修改站点配置信息
 */

// ================================
// 数据库配置
// ================================
define('DB_HOST', 'localhost');                    // 数据库主机地址，通常为 localhost
define('DB_NAME', '你的数据库名');                   // 在宝塔面板创建的数据库名
define('DB_USER', '你的数据库用户名');               // 数据库用户名
define('DB_PASS', '你的数据库密码');                 // 数据库密码

// ================================
// 站点配置
// ================================
define('SITE_NAME', '简易论坛');                    // 网站名称
define('SITE_URL', 'https://你的域名');             // 网站URL，注意协议（http或https）

// ================================
// 安全配置（可选）
// ================================
// 会话配置
ini_set('session.cookie_httponly', 1);             // 防止XSS攻击
ini_set('session.use_strict_mode', 1);             // 严格会话模式
ini_set('session.cookie_secure', 1);               // HTTPS下启用（如果使用HTTP请注释此行）

// 错误报告配置
error_reporting(E_ALL);                            // 开发环境：显示所有错误
ini_set('display_errors', 0);                     // 生产环境：不显示错误到页面
ini_set('log_errors', 1);                         // 记录错误到日志

// ================================
// 数据库连接
// ================================
try {
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", 
        DB_USER, 
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
        ]
    );
} catch(PDOException $e) {
    // 生产环境下不要显示具体的数据库错误信息
    error_log("数据库连接失败: " . $e->getMessage());
    die("数据库连接失败，请联系管理员。");
}

// ================================
// 时区设置
// ================================
date_default_timezone_set('Asia/Shanghai');        // 设置时区为中国标准时间

?>
