<?php
// 用户认证函数
function login_user($username, $password) {
    global $pdo;
    
    $stmt = $pdo->prepare("SELECT id, username, password FROM users WHERE username = ?");
    $stmt->execute([$username]);
    $user = $stmt->fetch();
    
    if ($user && password_verify($password, $user['password'])) {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        return true;
    }
    return false;
}

// 注册用户
function register_user($username, $email, $password) {
    global $pdo;
    
    // 检查用户名是否已存在
    $stmt = $pdo->prepare("SELECT id FROM users WHERE username = ? OR email = ?");
    $stmt->execute([$username, $email]);
    if ($stmt->fetch()) {
        return false;
    }
    
    // 创建新用户
    $hashed_password = password_hash($password, PASSWORD_DEFAULT);
    $stmt = $pdo->prepare("INSERT INTO users (username, email, password, created_at) VALUES (?, ?, ?, NOW())");
    return $stmt->execute([$username, $email, $hashed_password]);
}

// 获取最新话题
function get_latest_topics($limit = 10) {
    global $pdo;
    
    $limit = (int)$limit;
    $stmt = $pdo->query("
        SELECT t.*, u.username, 
               (SELECT COUNT(*) FROM posts p WHERE p.topic_id = t.id) as reply_count
        FROM topics t 
        JOIN users u ON t.user_id = u.id 
        ORDER BY t.created_at DESC 
        LIMIT $limit
    ");
    return $stmt->fetchAll();
}

// 获取话题详情
function get_topic($id) {
    global $pdo;
    
    $stmt = $pdo->prepare("
        SELECT t.*, u.username 
        FROM topics t 
        JOIN users u ON t.user_id = u.id 
        WHERE t.id = ?
    ");
    $stmt->execute([$id]);
    return $stmt->fetch();
}

// 获取话题的回复
function get_topic_posts($topic_id) {
    global $pdo;
    
    $stmt = $pdo->prepare("
        SELECT p.*, u.username 
        FROM posts p 
        JOIN users u ON p.user_id = u.id 
        WHERE p.topic_id = ? 
        ORDER BY p.created_at ASC
    ");
    $stmt->execute([$topic_id]);
    return $stmt->fetchAll();
}

// 创建新话题
function create_topic($title, $content, $user_id) {
    global $pdo;
    
    try {
        $pdo->beginTransaction();
        
        // 创建话题
        $stmt = $pdo->prepare("INSERT INTO topics (title, user_id, created_at) VALUES (?, ?, NOW())");
        $stmt->execute([$title, $user_id]);
        $topic_id = $pdo->lastInsertId();
        
        // 创建第一个帖子
        $stmt = $pdo->prepare("INSERT INTO posts (topic_id, user_id, content, created_at) VALUES (?, ?, ?, NOW())");
        $stmt->execute([$topic_id, $user_id, $content]);
        
        $pdo->commit();
        return $topic_id;
    } catch (Exception $e) {
        $pdo->rollback();
        return false;
    }
}

// 添加回复
function add_reply($topic_id, $content, $user_id) {
    global $pdo;
    
    $stmt = $pdo->prepare("INSERT INTO posts (topic_id, user_id, content, created_at) VALUES (?, ?, ?, NOW())");
    return $stmt->execute([$topic_id, $user_id, $content]);
}

// 格式化时间
function time_ago($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return '刚刚';
    if ($time < 3600) return floor($time/60) . '分钟前';
    if ($time < 86400) return floor($time/3600) . '小时前';
    if ($time < 2592000) return floor($time/86400) . '天前';
    
    return date('Y-m-d', strtotime($datetime));
}

// 检查用户是否登录
function is_logged_in() {
    return isset($_SESSION['user_id']);
}

// 安全输出HTML
function safe_output($text) {
    return htmlspecialchars($text, ENT_QUOTES, 'UTF-8');
}
?>
