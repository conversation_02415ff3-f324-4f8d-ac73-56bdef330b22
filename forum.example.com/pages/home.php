<?php
// 获取论坛统计信息
try {
    $stmt = $pdo->query("SELECT COUNT(*) FROM topics");
    $total_topics = $stmt->fetchColumn();

    $stmt = $pdo->query("SELECT COUNT(*) FROM posts");
    $total_posts = $stmt->fetchColumn();

    $stmt = $pdo->query("SELECT COUNT(*) FROM users");
    $total_users = $stmt->fetchColumn();

    // 获取最新话题
    $topics = get_latest_topics(5);
} catch (Exception $e) {
    $total_topics = 0;
    $total_posts = 0;
    $total_users = 0;
    $topics = [];
}
?>

<div class="row">
    <!-- 欢迎卡片 -->
    <div class="col-md-8 mb-4">
        <div class="card welcome-card">
            <div class="card-body">
                <h2><i class="bi bi-house-door"></i> 欢迎来到简易论坛</h2>
                <p class="mb-3">这是一个现代化的论坛系统，支持移动端和PC端访问。在这里您可以：</p>
                <div class="row">
                    <div class="col-md-4">
                        <i class="bi bi-check-circle"></i> 发布话题和参与讨论
                    </div>
                    <div class="col-md-4">
                        <i class="bi bi-check-circle"></i> 与其他用户交流互动
                    </div>
                    <div class="col-md-4">
                        <i class="bi bi-check-circle"></i> 分享知识和经验
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 统计信息 -->
    <div class="col-md-4 mb-4">
        <div class="card stats-card">
            <div class="card-body text-center">
                <h5><i class="bi bi-bar-chart"></i> 论坛统计</h5>
                <div class="row mt-3">
                    <div class="col-4">
                        <h4><?php echo $total_topics; ?></h4>
                        <small>话题</small>
                    </div>
                    <div class="col-4">
                        <h4><?php echo $total_posts; ?></h4>
                        <small>帖子</small>
                    </div>
                    <div class="col-4">
                        <h4><?php echo $total_users; ?></h4>
                        <small>用户</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 最新话题 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="bi bi-chat-dots"></i> 最新话题</h5>
                <?php if (is_logged_in()): ?>
                    <a href="index.php?page=new_topic" class="btn btn-primary btn-sm">
                        <i class="bi bi-plus-circle"></i> 发布话题
                    </a>
                <?php endif; ?>
            </div>
            <div class="card-body">
                <?php if (empty($topics)): ?>
                    <div class="text-center py-4">
                        <i class="bi bi-chat-dots-fill text-muted" style="font-size: 3rem;"></i>
                        <p class="text-muted mt-2">还没有话题，快来发布第一个话题吧！</p>
                        <?php if (!is_logged_in()): ?>
                            <a href="index.php?page=register" class="btn btn-primary">立即注册</a>
                        <?php endif; ?>
                    </div>
                <?php else: ?>
                    <?php foreach ($topics as $topic): ?>
                        <div class="forum-card border-bottom pb-3 mb-3">
                            <div class="d-flex justify-content-between align-items-start">
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">
                                        <a href="index.php?page=topic&id=<?php echo $topic['id']; ?>" class="text-decoration-none">
                                            <?php echo safe_output($topic['title']); ?>
                                        </a>
                                    </h6>
                                    <div class="topic-meta">
                                        <i class="bi bi-person"></i> <?php echo safe_output($topic['username']); ?>
                                        <i class="bi bi-clock ms-2"></i> <?php echo time_ago($topic['created_at']); ?>
                                        <i class="bi bi-chat ms-2"></i> <?php echo $topic['reply_count']; ?> 回复
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
