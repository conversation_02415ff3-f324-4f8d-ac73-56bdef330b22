<?php
session_start();
require_once '../config.php';
require_once '../functions.php';

// 检查管理员权限
function is_admin() {
    return isset($_SESSION['user_id']) && $_SESSION['username'] === 'admin';
}

if (!is_admin()) {
    header('Location: ../index.php?page=login');
    exit;
}

// 处理操作
$action = isset($_GET['action']) ? $_GET['action'] : 'dashboard';
$message = '';

if ($_POST) {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'delete_topic':
                $topic_id = $_POST['topic_id'];
                try {
                    $stmt = $pdo->prepare("DELETE FROM posts WHERE topic_id = ?");
                    $stmt->execute([$topic_id]);
                    $stmt = $pdo->prepare("DELETE FROM topics WHERE id = ?");
                    $stmt->execute([$topic_id]);
                    $message = '话题删除成功！';
                } catch (Exception $e) {
                    $message = '删除失败：' . $e->getMessage();
                }
                break;
            
            case 'delete_user':
                $user_id = $_POST['user_id'];
                if ($user_id != 1) {
                    try {
                        $stmt = $pdo->prepare("DELETE FROM posts WHERE user_id = ?");
                        $stmt->execute([$user_id]);
                        $stmt = $pdo->prepare("DELETE FROM topics WHERE user_id = ?");
                        $stmt->execute([$user_id]);
                        $stmt = $pdo->prepare("DELETE FROM users WHERE id = ?");
                        $stmt->execute([$user_id]);
                        $message = '用户删除成功！';
                    } catch (Exception $e) {
                        $message = '删除失败：' . $e->getMessage();
                    }
                } else {
                    $message = '不能删除管理员用户！';
                }
                break;
        }
    }
}

// 获取统计数据
$stats = [];
$stats['total_users'] = $pdo->query("SELECT COUNT(*) FROM users")->fetchColumn();
$stats['total_topics'] = $pdo->query("SELECT COUNT(*) FROM topics")->fetchColumn();
$stats['total_posts'] = $pdo->query("SELECT COUNT(*) FROM posts")->fetchColumn();
$stats['today_topics'] = $pdo->query("SELECT COUNT(*) FROM topics WHERE DATE(created_at) = CURDATE()")->fetchColumn();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员后台 - 简易论坛</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            border-radius: 5px;
            margin: 2px 0;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255,255,255,0.1);
        }
        .stats-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
        }
        .content-area {
            background-color: #f8f9fa;
            min-height: 100vh;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-2 sidebar p-3">
                <h4 class="text-white mb-4">
                    <i class="bi bi-shield-check"></i> 管理后台
                </h4>
                <nav class="nav flex-column">
                    <a class="nav-link <?php echo $action == 'dashboard' ? 'active' : ''; ?>" href="?action=dashboard">
                        <i class="bi bi-speedometer2"></i> 仪表盘
                    </a>
                    <a class="nav-link <?php echo $action == 'users' ? 'active' : ''; ?>" href="?action=users">
                        <i class="bi bi-people"></i> 用户管理
                    </a>
                    <a class="nav-link <?php echo $action == 'topics' ? 'active' : ''; ?>" href="?action=topics">
                        <i class="bi bi-chat-dots"></i> 话题管理
                    </a>
                    <hr class="text-white">
                    <a class="nav-link" href="../index.php">
                        <i class="bi bi-house"></i> 返回前台
                    </a>
                    <a class="nav-link" href="../index.php?page=logout">
                        <i class="bi bi-box-arrow-right"></i> 退出登录
                    </a>
                </nav>
            </div>
            <div class="col-md-10 content-area p-4">
                <?php if ($message): ?>
                    <div class="alert alert-<?php echo strpos($message, '成功') !== false ? 'success' : 'danger'; ?> alert-dismissible fade show">
                        <?php echo $message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                <?php
                switch ($action) {
                    case 'users':
                        include 'users.php';
                        break;
                    case 'topics':
                        include 'topics.php';
                        break;
                    default:
                        include 'dashboard.php';
                }
                ?>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function confirmDelete(message) {
            return confirm(message || '确定要删除吗？此操作不可恢复！');
        }
    </script>
</body>
</html>
