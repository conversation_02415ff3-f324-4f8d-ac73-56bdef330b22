<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="bi bi-people"></i> 用户管理</h2>
    <div>
        <span class="badge bg-primary">总用户: <?php echo $stats['total_users']; ?></span>
    </div>
</div>

<?php
$users = $pdo->query("
    SELECT u.*, 
           COUNT(DISTINCT t.id) as topic_count,
           COUNT(DISTINCT p.id) as post_count
    FROM users u
    LEFT JOIN topics t ON u.id = t.user_id
    LEFT JOIN posts p ON u.id = p.user_id
    GROUP BY u.id
    ORDER BY u.created_at DESC
")->fetchAll();
?>

<div class="card">
    <div class="card-header">
        <h5><i class="bi bi-table"></i> 用户列表</h5>
    </div>
    <div class="card-body">
        <?php if ($users): ?>
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>ID</th>
                            <th>用户名</th>
                            <th>邮箱</th>
                            <th>注册时间</th>
                            <th>话题数</th>
                            <th>帖子数</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($users as $user): ?>
                            <tr>
                                <td><?php echo $user['id']; ?></td>
                                <td>
                                    <strong><?php echo safe_output($user['username']); ?></strong>
                                    <?php if ($user['username'] === 'admin'): ?>
                                        <span class="badge bg-danger ms-1">管理员</span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo safe_output($user['email']); ?></td>
                                <td>
                                    <small><?php echo date('Y-m-d H:i', strtotime($user['created_at'])); ?></small>
                                </td>
                                <td>
                                    <span class="badge bg-primary"><?php echo $user['topic_count']; ?></span>
                                </td>
                                <td>
                                    <span class="badge bg-info"><?php echo $user['post_count']; ?></span>
                                </td>
                                <td>
                                    <?php if ($user['username'] !== 'admin'): ?>
                                        <form method="POST" action="?action=users" style="display: inline;" 
                                              onsubmit="return confirmDelete('确定要删除用户 <?php echo safe_output($user['username']); ?> 吗？')">
                                            <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                            <button type="submit" name="action" value="delete_user" 
                                                    class="btn btn-sm btn-danger">
                                                <i class="bi bi-trash"></i> 删除
                                            </button>
                                        </form>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php else: ?>
            <div class="text-center py-4">
                <i class="bi bi-people text-muted" style="font-size: 3rem;"></i>
                <p class="text-muted mt-2">暂无用户数据</p>
            </div>
        <?php endif; ?>
    </div>
</div>
