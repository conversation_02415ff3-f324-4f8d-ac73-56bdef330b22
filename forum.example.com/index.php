<?php require_once "security.php"; ?>
<?php
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 0); // 不显示错误到页面

try {
    require_once 'config.php';
    require_once 'functions.php';
} catch (Exception $e) {
    // 如果有错误，显示简单的错误页面
    die('系统初始化失败，请稍后再试。');
}

$page = isset($_GET['page']) ? $_GET['page'] : 'home';
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简易论坛 - 现代化社区</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">     
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet"> 
    <style>
        body { background-color: #f8f9fa; }
        .navbar-brand { font-weight: bold; }
        .forum-card { transition: transform 0.2s; }
        .forum-card:hover { transform: translateY(-2px); }
        .topic-meta { color: #6c757d; font-size: 0.9em; }
        .stats-card { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }
        .welcome-card { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white; }
        .navbar-toggler { border: none; }
        .navbar-toggler:focus { box-shadow: none; }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="bi bi-chat-dots"></i> 简易论坛
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">首页</a>
                    </li>
                    <?php if (is_logged_in()): ?>
                    <li class="nav-item">
                        <a class="nav-link" href="index.php?page=new_topic">发布话题</a>
                    </li>
                    <?php endif; ?>
                </ul>
                <ul class="navbar-nav">
                    <?php if (is_logged_in()): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="bi bi-person-circle"></i> <?php echo safe_output($_SESSION['username']); ?>
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="index.php?page=logout">退出登录</a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="index.php?page=login">登录</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="index.php?page=register">注册</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container mt-4">
        <?php
        try {
            switch ($page) {
                case 'login':
                    include 'pages/login.php';
                    break;
                case 'register':
                    include 'pages/register.php';
                    break;
                case 'logout':
                    session_destroy();
                    header('Location: index.php');
                    exit;
                case 'new_topic':
                    if (!is_logged_in()) {
                        header('Location: index.php?page=login');
                        exit;
                    }
                    include 'pages/new_topic.php';
                    break;
                case 'topic':
                    include 'pages/topic.php';
                    break;
                default:
                    include 'pages/home.php';
            }
        } catch (Exception $e) {
            echo '<div class="alert alert-danger">页面加载失败，请稍后再试。</div>';
        }
        ?>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
